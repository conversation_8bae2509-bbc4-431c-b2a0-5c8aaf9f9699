from freqtrade import FreqtradeBot
from freqtrade.configuration import Configuration
from freqtrade.optimize.hyperopt import Hyperopt
from src.hyperopts.sample_hyperopt_loss import SampleHyperOptLoss

def main():
    # Load configuration
    config = Configuration.from_file('config.json')

    # Initialize the trading bot
    bot = FreqtradeBot(config)

    # Set up hyperopt with the custom loss function
    hyperopt = Hyperopt(bot, SampleHyperOptLoss)

    # Start the trading process
    bot.start()

if __name__ == "__main__":
    main()
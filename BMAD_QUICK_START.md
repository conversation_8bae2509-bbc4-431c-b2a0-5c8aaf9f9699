# 🚀 BMAD Trading System - Quick Start Guide

## Directe Start Instructies

### 1. Laatste Controle Strategieën
```bash
cd /workspaces/freqtrade
freqtrade list-strategies
```

### 2. Download Data (indien nodig)
```bash
freqtrade download-data --exchange binance --pairs BTC/USDT ETH/USDT BNB/USDT --timeframes 5m 15m --days 7
```

### 3. Start BMAD Trading System
```bash
python start_bmad_trading.py
```

## 📊 Portfolio Configuratie

### Standaard Allocatie:
- **Accumulation**: 30% ($3,000) - Conservatief
- **Markup**: 30% ($3,000) - Agressief  
- **Distribution**: 20% ($2,000) - Defensief
- **Pro Markup**: 20% ($2,000) - Premium (standaard uit)

### Risk Management:
- **Max Drawdown**: 20% portfolio, 15% per strategie
- **Protection**: StoplossGuard, MaxDrawdown, LowProfitPairs
- **Emergency Stop**: Automatisch bij extreme verliezen

## 🎯 Snelle Trading Start

### Optie 1: Dry Run (Aanbevolen voor start)
```bash
freqtrade trade --config config_bmad_portfolio.json --strategy Innovars_BMAD_Accumulation --dry-run
```

### Optie 2: Live Trading (na testing)
```bash
# Eerst exchange API keys toevoegen aan config
freqtrade trade --config config_bmad_portfolio.json --strategy Innovars_BMAD_Accumulation
```

### Optie 3: Multi-Strategy Portfolio
```bash
# Start alle strategieën tegelijk (geavanceerd)
python start_bmad_trading.py
```

## 📱 Telegram Setup (Optioneel)

1. **Maak Telegram Bot**:
   - Message @BotFather op Telegram
   - `/newbot` en volg instructies
   - Kopieer bot token

2. **Update Config**:
   ```json
   "telegram": {
     "enabled": true,
     "token": "YOUR_BOT_TOKEN_HERE",
     "chat_id": "YOUR_CHAT_ID_HERE"
   }
   ```

3. **Portfolio Commands**:
   - `/portfolio_status` - Portfolio overzicht
   - `/strategy_on Accumulation` - Activeer strategie
   - `/set_allocation Markup 40` - Zet allocatie op 40%

## 🔧 Monitoring & Control

### Web Interface:
- **URL**: http://localhost:8080
- **Login**: freqtrade / your-password-here
- **Features**: Live trades, performance, logs

### Log Files:
```bash
# Trading logs
tail -f bmad_trading.log

# Freqtrade logs  
tail -f user_data/logs/freqtrade.log
```

### Stop Trading:
```bash
# Stop gracefully
kill $(cat bmad_trading_pid.txt)

# Force stop
pkill -f freqtrade
```

## ⚡ Snelle Test Sequence

### 1. Validatie Test (2 minuten)
```bash
# Test strategy loading
freqtrade list-strategies

# Test indicators
freqtrade backtesting --strategy Innovars_BMAD_Accumulation --timerange 20250601-20250602 --dry-run
```

### 2. Dry Run Test (5 minuten)
```bash
# Start dry run
freqtrade trade --config config_bmad_portfolio.json --strategy Innovars_BMAD_Accumulation --dry-run

# Check in andere terminal
freqtrade status --config config_bmad_portfolio.json
```

### 3. Live Trading (na validatie)
```bash
# Update config met echte API keys
# Start live trading
freqtrade trade --config config_bmad_portfolio.json --strategy Innovars_BMAD_Accumulation
```

## 🎯 Aanbevolen Start Strategie

### Voor Beginners:
1. **Start met Accumulation** (conservatief)
2. **Dry run voor 24 uur**
3. **Monitor performance**
4. **Voeg Markup toe** indien succesvol

### Voor Ervaren Traders:
1. **Start Multi-Strategy Portfolio**
2. **Gebruik alle 3 strategieën**
3. **Monitor via Telegram**
4. **Optimaliseer allocaties**

## 🚨 Belangrijke Waarschuwingen

### Risico's:
- **Cryptocurrency trading is risicovol**
- **Start altijd met kleine bedragen**
- **Monitor regelmatig**
- **Gebruik stop-losses**

### Technisch:
- **Test eerst in dry-run mode**
- **Controleer API rate limits**
- **Backup configuraties**
- **Monitor server resources**

## 📞 Support & Troubleshooting

### Veelvoorkomende Problemen:

1. **Strategy niet gevonden**:
   ```bash
   # Check strategy files
   ls user_data/strategies/Innovars_BMAD_*.py
   ```

2. **Data ontbreekt**:
   ```bash
   # Download data
   freqtrade download-data --exchange binance --pairs BTC/USDT --timeframes 5m 15m --days 7
   ```

3. **API errors**:
   - Controleer exchange API keys
   - Controleer rate limits
   - Controleer internet verbinding

### Debug Commands:
```bash
# Verbose logging
freqtrade trade --config config_bmad_portfolio.json --strategy Innovars_BMAD_Accumulation --dry-run --verbosity 3

# Test strategy
freqtrade test-pairlist --config config_bmad_portfolio.json

# Check exchange
freqtrade list-exchanges
```

---

## 🎉 Ready to Trade!

**Kies je start optie:**

### 🟢 Veilige Start (Aanbevolen):
```bash
python start_bmad_trading.py
```

### 🟡 Snelle Test:
```bash
freqtrade trade --config config_bmad_portfolio.json --strategy Innovars_BMAD_Accumulation --dry-run
```

### 🔴 Direct Live (Alleen voor experts):
```bash
# Voeg eerst API keys toe aan config!
freqtrade trade --config config_bmad_portfolio.json --strategy Innovars_BMAD_Accumulation
```

**Happy Trading! 🚀📈**

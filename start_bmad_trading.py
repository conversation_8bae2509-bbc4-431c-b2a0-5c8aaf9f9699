#!/usr/bin/env python3
"""
BMAD Trading Startup Script

Start alle BMAD strategieën voor live trading met portfolio management.

Auteur: Innovars Labs
Versie: 1.0
Datum: 2024
"""

import os
import sys
import json
import subprocess
import time
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bmad_trading.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BMADTradingStarter:
    """
    Starter voor BMAD trading systeem
    """
    
    def __init__(self):
        self.freqtrade_path = "/workspaces/freqtrade"
        self.strategies = [
            "Innovars_BMAD_Accumulation",
            "Innovars_BMAD_Markup", 
            "Innovars_BMAD_Distribution_Markdown"
        ]
        
    def check_prerequisites(self) -> bool:
        """
        Controleert of alles klaar is voor trading
        """
        logger.info("🔍 Checking prerequisites...")
        
        # Check if strategies exist
        for strategy in self.strategies:
            strategy_file = Path(f"user_data/strategies/{strategy}.py")
            if not strategy_file.exists():
                logger.error(f"❌ Strategy file missing: {strategy_file}")
                return False
            logger.info(f"✅ Strategy found: {strategy}")
        
        # Check config file
        config_file = Path("config_bmad_portfolio.json")
        if not config_file.exists():
            logger.error(f"❌ Config file missing: {config_file}")
            return False
        logger.info(f"✅ Config file found: {config_file}")
        
        # Check if data is available
        data_dir = Path("user_data/data/binance")
        if not data_dir.exists():
            logger.warning("⚠️ No data directory found, will download data")
            return self.download_data()
        
        logger.info("✅ All prerequisites met")
        return True
    
    def download_data(self) -> bool:
        """
        Download benodigde data voor trading
        """
        logger.info("📥 Downloading trading data...")
        
        pairs = ["BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT"]
        timeframes = ["5m", "15m", "1h"]
        
        cmd = [
            "freqtrade", "download-data",
            "--exchange", "binance",
            "--pairs"] + pairs + [
            "--timeframes"] + timeframes + [
            "--days", "30"
        ]
        
        try:
            result = subprocess.run(
                cmd, 
                cwd=self.freqtrade_path,
                capture_output=True, 
                text=True, 
                timeout=300
            )
            
            if result.returncode == 0:
                logger.info("✅ Data download completed")
                return True
            else:
                logger.error(f"❌ Data download failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ Data download timeout")
            return False
        except Exception as e:
            logger.error(f"❌ Data download error: {e}")
            return False
    
    def validate_strategies(self) -> bool:
        """
        Valideer dat alle strategieën correct laden
        """
        logger.info("🧪 Validating strategies...")
        
        cmd = ["freqtrade", "list-strategies"]
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.freqtrade_path,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                output = result.stdout
                for strategy in self.strategies:
                    if strategy in output:
                        logger.info(f"✅ Strategy validated: {strategy}")
                    else:
                        logger.error(f"❌ Strategy not found: {strategy}")
                        return False
                return True
            else:
                logger.error(f"❌ Strategy validation failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Strategy validation error: {e}")
            return False
    
    def start_dry_run_trading(self) -> bool:
        """
        Start dry run trading met alle strategieën
        """
        logger.info("🚀 Starting BMAD dry run trading...")
        
        # Start met eerste strategie
        strategy = self.strategies[0]  # Start met Accumulation
        
        cmd = [
            "freqtrade", "trade",
            "--config", "config_bmad_portfolio.json",
            "--strategy", strategy,
            "--dry-run"
        ]
        
        try:
            logger.info(f"Starting {strategy} in dry-run mode...")
            
            # Start process in background
            process = subprocess.Popen(
                cmd,
                cwd=self.freqtrade_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Wait a bit to see if it starts successfully
            time.sleep(10)
            
            if process.poll() is None:  # Process is still running
                logger.info(f"✅ {strategy} started successfully (PID: {process.pid})")
                
                # Save process info
                with open("bmad_trading_pid.txt", "w") as f:
                    f.write(str(process.pid))
                
                return True
            else:
                stdout, stderr = process.communicate()
                logger.error(f"❌ {strategy} failed to start:")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error starting trading: {e}")
            return False
    
    def show_trading_status(self):
        """
        Toont trading status
        """
        logger.info("📊 BMAD Trading Status:")
        logger.info("=" * 50)
        
        # Check if process is running
        pid_file = Path("bmad_trading_pid.txt")
        if pid_file.exists():
            with open(pid_file, "r") as f:
                pid = f.read().strip()
            
            try:
                # Check if process exists
                os.kill(int(pid), 0)
                logger.info(f"✅ Trading bot is running (PID: {pid})")
                
                # Show some basic info
                logger.info("📈 Active Strategies:")
                for strategy in self.strategies:
                    logger.info(f"  • {strategy}")
                
                logger.info("\n🔗 Useful commands:")
                logger.info("  • Check logs: tail -f bmad_trading.log")
                logger.info("  • Stop trading: kill $(cat bmad_trading_pid.txt)")
                logger.info("  • Web UI: http://localhost:8080")
                
            except OSError:
                logger.warning("⚠️ Trading bot process not found")
        else:
            logger.warning("⚠️ No trading bot running")
    
    def create_telegram_commands_help(self):
        """
        Toont beschikbare Telegram commando's
        """
        logger.info("\n📱 Telegram Commands:")
        logger.info("=" * 30)
        logger.info("/status - Bot status")
        logger.info("/profit - Profit overview")
        logger.info("/balance - Account balance")
        logger.info("/trades - Recent trades")
        logger.info("/performance - Performance stats")
        logger.info("/stop - Stop trading")
        logger.info("/start - Start trading")
        logger.info("/reload_config - Reload configuration")
        
        # Custom BMAD commands
        logger.info("\n🎯 BMAD Portfolio Commands:")
        logger.info("/portfolio_status - Portfolio overview")
        logger.info("/strategy_on <name> - Activate strategy")
        logger.info("/strategy_off <name> - Deactivate strategy")
        logger.info("/set_allocation <strategy> <percent> - Set allocation")
        logger.info("/rebalance_portfolio - Rebalance portfolio")
    
    def run_startup_sequence(self):
        """
        Volledige startup sequence
        """
        logger.info("🎯 BMAD Trading System Startup")
        logger.info("=" * 50)
        
        # Step 1: Check prerequisites
        if not self.check_prerequisites():
            logger.error("❌ Prerequisites not met, aborting")
            return False
        
        # Step 2: Validate strategies
        if not self.validate_strategies():
            logger.error("❌ Strategy validation failed, aborting")
            return False
        
        # Step 3: Start trading
        if not self.start_dry_run_trading():
            logger.error("❌ Failed to start trading, aborting")
            return False
        
        # Step 4: Show status
        self.show_trading_status()
        
        # Step 5: Show commands
        self.create_telegram_commands_help()
        
        logger.info("\n🎉 BMAD Trading System started successfully!")
        logger.info("Monitor the logs and web UI for trading activity.")
        
        return True

def main():
    """
    Main startup function
    """
    starter = BMADTradingStarter()
    
    try:
        success = starter.run_startup_sequence()
        if success:
            logger.info("✅ Startup completed successfully")
            
            # Keep script running to monitor
            logger.info("Press Ctrl+C to stop monitoring...")
            try:
                while True:
                    time.sleep(60)  # Check every minute
                    # Could add health checks here
            except KeyboardInterrupt:
                logger.info("👋 Monitoring stopped by user")
        else:
            logger.error("❌ Startup failed")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
